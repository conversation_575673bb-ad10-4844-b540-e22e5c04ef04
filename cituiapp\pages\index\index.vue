<template>
	<view class="page-container">
		<!-- 固定顶部导航 - 精仿原型HTML -->
		<view class="fixed-header" :style="topSafeAreaStyle">
			<view class="header-content">
				<!-- 左侧管理员图标 -->
				<view class="header-icon left-icon" @click="handleAdminLogin">
					<!-- 使用CSS伪元素模拟图标 -->
					<view class="icon-account"></view>
				</view>
				
				<!-- 中间搜索框 -->
				<view class="search-wrapper">
					<view class="search-container">
						<input
							v-model="searchValue"
							type="text"
							placeholder="搜索APP"
							class="search-input"
							@confirm="handleSearch"
							@input="handleSearchInput"
						/>
						<view v-if="!isSearching" class="search-icon" @click="handleSearch">
							<view class="icon-search"></view>
						</view>
						<view v-else class="clear-icon" @click="clearSearch">
							<view class="icon-clear">×</view>
						</view>
					</view>
				</view>
				
				<!-- 右侧通知图标 -->
				<view class="header-icon right-icon" @click="handleNotification">
					<!-- 使用CSS伪元素模拟图标 -->
					<view class="icon-bell"></view>
				</view>
			</view>
		</view>
		
		<!-- 页面内容区域 - 支持下拉刷新和上滑加载 -->
		<view class="content-container" :style="contentTopMarginStyleWithSimpleHeader">
			<scroll-view 
				class="scroll-container"
				scroll-y
				refresher-enabled
				:refresher-triggered="isRefreshing"
				@refresherrefresh="onRefresh"
				@refresherrestore="onRestore"
				@scrolltolower="onLoadMore"
				lower-threshold="100"
			>
				<view class="content-inner">
					<!-- 标题区域 -->
					<view class="section-title">
						<text v-if="!isSearching" class="title-text">新手免费试看</text>
						<text v-else class="title-text">搜索结果：{{ searchKeyword }}</text>
					</view>
					
					<!-- 卡片列表 -->
					<view class="card-list">
						<view 
							v-for="(item, index) in appList" 
							:key="item.id"
							class="app-card"
							:class="getCardGradientClass(index)"
							@click="handleCardClick(item)"
						>
							<!-- 卡片头部 -->
							<view class="card-header">
								<view class="app-info">
									<view class="app-icon" :class="getIconClass(item.type)">
										<!-- 优先显示真实LOGO，失败时回退到emoji图标 -->
										<image
											v-if="item.logo_url"
											:src="item.logo_url"
											class="app-logo-image"
											@error="handleLogoError(item)"
											mode="aspectFill"
										/>
										<text v-else class="icon-symbol">{{ getIconSymbol(item.type) }}</text>
									</view>
									<view class="app-details">
										<text class="app-name">{{ item.name }}</text>
										<text class="update-time">{{ item.updateTime }}</text>
									</view>
								</view>
								<view class="app-stats">
									<view class="rating">
										<view class="star-icon">⭐</view>
										<text class="rating-text">{{ item.rating }}</text>
									</view>
									<view class="download-count">
										<view class="download-icon">⬇</view>
										<text class="count-text">{{ item.downloadCount }}次</text>
									</view>
								</view>
							</view>
							
							<!-- 标签区域 -->
							<view class="tags-container">
								<view 
									v-for="tag in item.tags" 
									:key="tag.text"
									class="tag"
									:class="getTagClass(tag.type)"
								>
									<text class="tag-text">{{ tag.emoji }}{{ tag.text }}</text>
								</view>
							</view>
							
							<!-- 卡片底部按钮 -->
							<view class="card-footer">
								<view class="trial-btn" @click.stop="handleTrial(item)">
									<text class="btn-text">{{ getButtonText(item.isInstalled, '下载赚钱') }}</text>
								</view>
								<view class="detail-btn" @click.stop="handleDetail(item)">
									<text class="detail-text">查看详情</text>
									<text class="arrow">›</text>
								</view>
							</view>
						</view>
					</view>
					
					<!-- 加载更多提示 -->
					<view class="load-more-container" v-if="showLoadMore && appList.length > 0">
						<u-loadmore
							:status="loadStatus"
							:load-text="loadText"
							margin-top="20"
							margin-bottom="20"
						/>
					</view>
					
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
	import { handleDownload } from '@/utils/download.js'
	import { batchCheckAppsInstalled, handleAppAction, getButtonText, isAppEnvironment } from '@/utils/appInstall.js'
	import safeAreaMixin from '@/mixins/safeArea.js'

	export default {
		mixins: [safeAreaMixin],
		data() {
			return {
				searchValue: '',
				isRefreshing: false,
				showLoadMore: false,
				loadStatus: 'loadmore', // loadmore, loading, nomore
				loadText: {
					loadmore: '点击或上拉加载更多',
					loading: '正在加载...',
					nomore: '没有更多了'
				},
				currentPage: 1,
				pageSize: 10,
				appList: [],
				isSearching: false, // 是否处于搜索状态
				searchKeyword: '' // 当前搜索关键词
			}
		},
		methods: {
			// 管理员登录
			handleAdminLogin() {
				console.log('管理员登录')
			},
			
			// 通知点击
			handleNotification() {
				console.log('通知点击')
			},
			
			// 下拉刷新
			onRefresh() {
				this.isRefreshing = true
				setTimeout(() => {
					// 刷新数据，保持当前搜索状态
					this.currentPage = 1
					this.loadMoreData(true)
					this.isRefreshing = false
				}, 1500)
			},
			
			// 刷新恢复
			onRestore() {
				this.isRefreshing = false
			},
			
			// 上滑加载更多
			onLoadMore() {
				// 检查是否正在加载或已经没有更多数据
				if (this.loadStatus !== 'loading' && this.loadStatus !== 'nomore') {
					this.loadMoreData()
				}
			},
			
			// 加载更多数据
			loadMoreData(isRefresh = false) {
				this.loadStatus = 'loading'
				this.showLoadMore = true

				// 如果是刷新，重置页码
				if (isRefresh) {
					this.currentPage = 1
				} else {
					// 非刷新情况下，先递增页码
					this.currentPage++
				}

				// 调用真实API
				const params = {
					page: this.currentPage,
					page_size: this.pageSize
				}

				// 如果处于搜索状态，添加搜索关键词
				if (this.isSearching && this.searchKeyword) {
					params.search = this.searchKeyword
				}

				uni.$u.http.get('/app/home', {params: params}).then(async res => {
					console.log('API返回数据:', res)

					if (isRefresh) {
						this.appList = res.list || []
					} else {
						this.appList = [...this.appList, ...(res.list || [])]
					}

					// 更新分页状态
					if (res.has_more) {
						this.loadStatus = 'loadmore'
						this.showLoadMore = true
					} else {
						this.loadStatus = 'nomore'
						// 如果有数据则显示"没有更多了"，否则隐藏
						this.showLoadMore = this.appList.length > 0
					}
					console.log('当前页码:', this.currentPage, '加载状态:', this.loadStatus);

					// 检测APP安装状态
					await this.checkAppsInstallStatus()

				}).catch(err => {
					console.error('加载数据失败:', err)
					// 请求失败时恢复页码
					if (!isRefresh) {
						this.currentPage--
					}
					this.loadStatus = 'loadmore'
					this.showLoadMore = this.appList.length > 0
					uni.showToast({
						title: '加载失败，请重试',
						icon: 'none'
					})
				})
			},
			
			// 获取卡片渐变样式
			getCardGradientClass(index) {
				const gradients = ['gradient-red', 'gradient-blue', 'gradient-green']
				return gradients[index % 3]
			},
			
			// 获取图标样式
			getIconClass(type) {
				const iconClasses = {
					coin: 'icon-coin',
					video: 'icon-video', 
					cash: 'icon-cash'
				}
				return iconClasses[type] || 'icon-coin'
			},
			
			// 获取图标符号
			getIconSymbol(type) {
				const symbols = {
					coin: '💰',
					video: '📺',
					cash: '💵'
				}
				return symbols[type] || '💰'
			},
			
			// 获取标签样式
			getTagClass(type) {
				return `tag-${type}`
			},
			
			// 卡片点击
			handleCardClick(item) {
				console.log('卡片点击:', item.name)
			},
			
			// 试用按钮
			handleTrial(item) {
				// 使用统一的APP操作处理方法
				handleAppAction(item, '下载赚钱')
			},
			
			// 详情按钮
			handleDetail(item) {
				// 跳转到详情页面，传递APP信息
				uni.navigateTo({
					url: `/pages/detail/detail?id=${item.id}&name=${encodeURIComponent(item.name)}`
				})
			},
			
			// 查看更多
			handleViewMore() {
				uni.showToast({
					title: '查看更多APP',
					icon: 'none'
				})
			},

			// 处理LOGO加载失败
			handleLogoError(item) {
				// 将logo_url设为空，触发回退到emoji图标
				item.logo_url = ''
			},

			// 处理搜索输入
			handleSearchInput(e) {
				// 如果搜索框被清空，恢复到非搜索状态
				if (!e.detail.value.trim()) {
					this.clearSearch()
				}
			},

			// 执行搜索
			handleSearch() {
				const keyword = this.searchValue.trim()
				if (!keyword) {
					this.clearSearch()
					return
				}

				this.isSearching = true
				this.searchKeyword = keyword
				this.currentPage = 1
				this.loadMoreData(true)
			},

			// 清空搜索
			clearSearch() {
				this.isSearching = false
				this.searchKeyword = ''
				this.searchValue = ''
				this.currentPage = 1
				this.loadMoreData(true)
			},

			// 获取按钮显示文字
			getButtonText(isInstalled, defaultText) {
				return getButtonText(isInstalled, defaultText)
			},

			// 检测APP列表的安装状态
			async checkAppsInstallStatus() {
				if (!isAppEnvironment() || this.appList.length === 0) {
					return
				}

				try {
					// 批量检测APP安装状态
					this.appList = await batchCheckAppsInstalled(this.appList)
					console.log('APP安装状态检测完成:', this.appList.map(app => ({
						name: app.name,
						package: app.package,
						isInstalled: app.isInstalled
					})))
				} catch (error) {
					console.error('检测APP安装状态失败:', error)
				}
			}
		},
		
		onLoad() {
			// 初始加载
			this.loadMoreData(true)
		}
	}
</script>

<style lang="scss" scoped>
.page-container {
	width: 100%;
	height: 100vh;
	background-color: #f5f5f5;
}

/* 固定顶部导航 - 精仿原型HTML样式 */
.fixed-header {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 999;
	background-color: #ffffff;
	border-bottom: 2rpx solid #f0f0f0; /* border-gray-100 */
	
	.header-content {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 32rpx; /* py-3 px-4 转换为rpx */
		min-height: 120rpx;
		
		/* 左右图标样式 */
		.header-icon {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 48rpx; /* text-2xl 对应大小 */
			height: 48rpx;
			cursor: pointer;
			
			/* 管理员账号图标 */
			.icon-account {
				width: 48rpx;
				height: 48rpx;
				background-color: #4b5563; /* text-gray-700 */
				mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M12 4a4 4 0 0 1 4 4a4 4 0 0 1-4 4a4 4 0 0 1-4-4a4 4 0 0 1 4-4m0 10c4.42 0 8 1.79 8 4v2H4v-2c0-2.21 3.58-4 8-4Z'/%3E%3C/svg%3E") no-repeat center;
				mask-size: contain;
			}
			
			/* 通知铃铛图标 */
			.icon-bell {
				width: 48rpx;
				height: 48rpx;
				background-color: #4b5563; /* text-gray-700 */
				mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M21 19v1H3v-1l2-2v-6c0-3.1 2.03-5.83 5-6.71V4a2 2 0 0 1 2-2a2 2 0 0 1 2 2v.29c2.97.88 5 3.61 5 6.71v6l2 2M12 23a2 2 0 0 1-2-2h4a2 2 0 0 1-2 2Z'/%3E%3C/svg%3E") no-repeat center;
				mask-size: contain;
			}
		}
		
		/* 中间搜索框区域 */
		.search-wrapper {
			flex: 1;
			margin: 0 32rpx; /* mx-4 */
			position: relative;
			
			.search-container {
				position: relative;
				width: 100%;
				
				.search-input {
					background-color: #f3f4f6; /* bg-gray-100 */
					border-radius: 50rpx; /* rounded-full */
					padding: 16rpx 32rpx; /* py-2 px-4 */
					font-size: 28rpx; /* text-sm */
					border: none;
					outline: none;
					color: #111827;
					
					&::placeholder {
						color: #9ca3af;
					}
					
					/* 移除默认的input样式 */
					&:focus {
						outline: none;
						border: none;
					}
				}
				
				.search-icon, .clear-icon {
					position: absolute;
					right: 24rpx; /* right-3 */
					top: 50%;
					transform: translateY(-50%);
					cursor: pointer;

					.icon-search {
						width: 32rpx;
						height: 32rpx;
						background-color: #6b7280; /* text-gray-500 */
						mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5l-1.5 1.5l-5-5v-.79l-.27-.27A6.516 6.516 0 0 1 9.5 16A6.5 6.5 0 0 1 3 9.5A6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14S14 12 14 9.5S12 5 9.5 5Z'/%3E%3C/svg%3E") no-repeat center;
						mask-size: contain;
					}

					.icon-clear {
						width: 32rpx;
						height: 32rpx;
						display: flex;
						align-items: center;
						justify-content: center;
						background-color: #9ca3af;
						border-radius: 50%;
						color: #ffffff;
						font-size: 24rpx;
						font-weight: bold;
					}
				}
			}
		}
	}
}

/* 内容区域 */
.content-container {
	/* padding-top 通过动态样式设置，适配不同设备的安全区域 */
	height: 100vh;
	background-color: #f9fafb; /* bg-gray-50 */
	
	.scroll-container {
		height: 100%;
		
		.content-inner {
			padding: 32rpx; /* px-4 pt-2 */
			
			/* 标题样式 */
			.section-title {
				margin-bottom: 24rpx;
				
				.title-text {
					font-size: 36rpx; /* text-lg */
					font-weight: bold;
					color: #111827;
				}
			}
			
			/* 卡片列表 */
			.card-list {
				.app-card {
					background: #ffffff;
					border-radius: 16rpx;
					padding: 32rpx;
					margin-bottom: 32rpx;
					box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.1);
					position: relative;
					overflow: hidden;
					
					/* 渐变背景 */
					&.gradient-red {
						background: linear-gradient(135deg, #fef2f2 0%, #ffffff 100%);
					}
					
					&.gradient-blue {
						background: linear-gradient(135deg, #eff6ff 0%, #ffffff 100%);
					}
					
					&.gradient-green {
						background: linear-gradient(135deg, #f0fdf4 0%, #ffffff 100%);
					}
					
					/* 卡片头部 */
					.card-header {
						display: flex;
						justify-content: space-between;
						align-items: center;
						margin-bottom: 24rpx;
						
						.app-info {
							display: flex;
							align-items: center;
							
							.app-icon {
								width: 48rpx;
								height: 48rpx;
								border-radius: 8rpx;
								display: flex;
								align-items: center;
								justify-content: center;
								margin-right: 16rpx;
								position: relative;
								overflow: hidden;

								&.icon-coin {
									background-color: #f59e0b;
								}

								&.icon-video {
									background-color: #ef4444;
								}

								&.icon-cash {
									background-color: #10b981;
								}

								.app-logo-image {
									width: 100%;
									height: 100%;
									border-radius: 8rpx;
								}

								.icon-symbol {
									font-size: 24rpx;
								}
							}
							
							.app-details {
								flex: 1;
								
								.app-name {
									display: block;
									font-size: 32rpx;
									font-weight: 600;
									color: #111827;
									margin-bottom: 4rpx;
								}
								
								.update-time {
									display: block;
									font-size: 22rpx;
									color: #9ca3af;
									font-weight: 400;
								}
							}
						}
						
						.app-stats {
							display: flex;
							gap: 32rpx;
							
							.rating, .download-count {
								display: flex;
								align-items: center;
								
								.star-icon, .download-icon {
									font-size: 24rpx;
									margin-right: 4rpx;
								}
								
								.rating-text, .count-text {
									font-size: 24rpx;
									color: #6b7280;
								}
							}
							
							.rating .rating-text {
								color: #f59e0b;
							}
						}
					}
					
					/* 标签区域 */
					.tags-container {
						display: flex;
						flex-wrap: wrap;
						gap: 16rpx;
						margin-bottom: 24rpx;
						
						.tag {
							border-radius: 24rpx;
							padding: 4rpx 12rpx;
							display: inline-flex;
							align-items: center;
							justify-content: center;
							min-height: 44rpx;
							
							.tag-text {
								font-size: 22rpx;
								font-weight: 500;
								line-height: 1;
							}
							
							&.tag-blue {
								background-color: #dbeafe;
								
								.tag-text {
									color: #2563eb;
								}
							}
							
							&.tag-gray {
								background-color: #f3f4f6;
								
								.tag-text {
									color: #4b5563;
								}
							}
							
							&.tag-green {
								background-color: #dcfce7;
								
								.tag-text {
									color: #16a34a;
								}
							}
							
							&.tag-red {
								background-color: #fee2e2;
								
								.tag-text {
									color: #dc2626;
								}
							}
							
							&.tag-amber {
								background-color: #fef3c7;
								
								.tag-text {
									color: #d97706;
								}
							}
						}
					}
					
					/* 卡片底部 */
					.card-footer {
						display: flex;
						justify-content: space-between;
						align-items: center;
						
						.trial-btn {
							background: linear-gradient(135deg, #ef4444, #dc2626);
							border-radius: 24rpx;
							padding: 12rpx 24rpx;
							
							.btn-text {
								color: #ffffff;
								font-size: 28rpx;
								font-weight: 600;
							}
						}
						
						.detail-btn {
							display: flex;
							align-items: center;
							
							.detail-text {
								color: #ef4444;
								font-size: 28rpx;
								font-weight: 600;
								margin-right: 8rpx;
							}
							
							.arrow {
								color: #ef4444;
								font-size: 32rpx;
								font-weight: bold;
							}
						}
					}
				}
			}
			
			/* 加载更多容器 */
			.load-more-container {
				margin: 32rpx 0;
				padding-bottom: 120rpx; /* 添加底部安全距离，避免被tabbar遮挡 */
			}
			
			/* 查看更多按钮 */
			.more-btn-container {
				text-align: center;
				margin-top: 24rpx;
				margin-bottom: 40rpx;
				
				.more-btn {
					display: inline-flex;
					align-items: center;
					
					.more-text {
						color: #3b82f6;
						font-size: 28rpx;
						font-weight: 500;
						margin-right: 8rpx;
					}
					
					.arrow {
						color: #3b82f6;
						font-size: 32rpx;
						font-weight: bold;
					}
				}
			}
		}
	}
}

/* 隐藏滚动条 */
::-webkit-scrollbar {
	display: none;
}
</style>
