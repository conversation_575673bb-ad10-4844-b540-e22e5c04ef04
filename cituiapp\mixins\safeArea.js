/**
 * 安全区域混入
 * 用于自动处理自定义导航栏页面的顶部安全距离适配
 */

import { getSystemSafeArea, getTopSafeDistanceRpx } from '@/common/utils/tools.js'

export default {
  data() {
    return {
      // 安全区域相关数据
      safeAreaInfo: {
        statusBarHeight: 0,
        topSafeDistance: 0,
        topSafeDistanceRpx: 0,
        platform: 'unknown'
      }
    }
  },
  
  computed: {
    // 计算顶部安全距离样式
    topSafeAreaStyle() {
      return {
        paddingTop: `${this.safeAreaInfo.topSafeDistanceRpx}rpx`
      }
    },

    // 计算固定头部的高度样式
    fixedHeaderStyle() {
      return {
        height: `${this.safeAreaInfo.topSafeDistanceRpx}rpx`,
        paddingTop: `${this.safeAreaInfo.statusBarHeight * 2}rpx` // 状态栏高度转rpx
      }
    },

    // 计算内容区域的顶部边距 - 基础版本（只考虑安全区域）
    contentTopMarginStyle() {
      return {
        marginTop: `${this.safeAreaInfo.topSafeDistanceRpx}rpx`
      }
    },

    // 计算内容区域的顶部边距 - 简单固定头部版本（如 index.vue, detail.vue）
    // 固定头部高度：安全区域 + 120rpx（header-content的min-height）
    contentTopMarginStyleWithSimpleHeader() {
      const totalHeight = this.safeAreaInfo.topSafeDistanceRpx + 120
      return {
        marginTop: `${totalHeight}rpx`
      }
    },

    // 计算内容区域的顶部边距 - 复杂固定头部版本（如 clue.vue, evaluation.vue）
    // 固定头部高度计算：
    // 1. 固定头部padding: 24rpx(上) + 16rpx(下) = 40rpx
    // 2. 搜索框区域: 80rpx(min-height) + 32rpx(padding上下) + 16rpx(margin-bottom) = 128rpx
    // 3. 标签导航区域: 32rpx(文字) + 16rpx(padding-bottom) + 16rpx(section padding-bottom) = 64rpx
    // 总计：安全区域 + 232rpx
    contentTopMarginStyleWithComplexHeader() {
      const totalHeight = this.safeAreaInfo.topSafeDistanceRpx + 232
      return {
        marginTop: `${totalHeight}rpx`
      }
    }
  },
  
  created() {
    // 初始化安全区域信息
    this.initSafeArea()
  },
  
  methods: {
    /**
     * 初始化安全区域信息
     */
    initSafeArea() {
      try {
        const safeAreaData = getSystemSafeArea()
        const topSafeDistanceRpx = getTopSafeDistanceRpx()
        
        this.safeAreaInfo = {
          statusBarHeight: safeAreaData.statusBarHeight,
          topSafeDistance: safeAreaData.topSafeDistance,
          topSafeDistanceRpx: topSafeDistanceRpx,
          platform: safeAreaData.platform,
          safeAreaInsets: safeAreaData.safeAreaInsets
        }
        
        console.log('安全区域信息初始化完成:', this.safeAreaInfo)
      } catch (error) {
        console.error('初始化安全区域信息失败:', error)
        // 设置默认值
        this.safeAreaInfo = {
          statusBarHeight: 20,
          topSafeDistance: 64,
          topSafeDistanceRpx: 128,
          platform: 'unknown',
          safeAreaInsets: { top: 20, bottom: 0, left: 0, right: 0 }
        }
      }
    },
    
    /**
     * 获取当前页面是否需要安全区域适配
     * 可以在具体页面中重写此方法来控制是否启用安全区域
     * @returns {boolean}
     */
    needSafeAreaAdapt() {
      // 默认返回true，表示需要安全区域适配
      // 具体页面可以重写此方法
      return true
    },
    
    /**
     * 获取安全区域信息
     * @returns {Object}
     */
    getSafeAreaInfo() {
      return this.safeAreaInfo
    },

    /**
     * 获取内容区域顶部间距的使用说明
     * @returns {Object} 包含不同样式的使用说明
     */
    getContentTopMarginStyleGuide() {
      return {
        contentTopMarginStyle: '基础版本，只考虑安全区域，适用于没有固定头部的页面',
        contentTopMarginStyleWithSimpleHeader: '简单固定头部版本，适用于只有一行导航栏的页面（如 index.vue, detail.vue）',
        contentTopMarginStyleWithComplexHeader: '复杂固定头部版本，适用于有搜索框和标签导航的页面（如 clue.vue, evaluation.vue）'
      }
    }
  }
}
