/*
 Navicat Premium Data Transfer

 Source Server         : AA本地数据库
 Source Server Type    : MySQL
 Source Server Version : 50726
 Source Host           : localhost:3306
 Source Schema         : citui

 Target Server Type    : MySQL
 Target Server Version : 50726
 File Encoding         : 65001

 Date: 19/08/2025 17:30:41
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for zc_admin_users
-- ----------------------------
DROP TABLE IF EXISTS `zc_admin_users`;
CREATE TABLE `zc_admin_users`  (
  `admin_id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '管理员ID',
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `password_hash` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码哈希',
  `real_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '真实姓名',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '邮箱地址',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '手机号码',
  `avatar_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '头像URL',
  `role` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'admin' COMMENT '角色',
  `permissions` json NULL COMMENT '权限列表',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用',
  `last_login_at` timestamp(0) NULL DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '最后登录IP',
  `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `updated_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`admin_id`) USING BTREE,
  UNIQUE INDEX `uk_username`(`username`) USING BTREE,
  UNIQUE INDEX `uk_email`(`email`) USING BTREE,
  UNIQUE INDEX `uk_phone`(`phone`) USING BTREE,
  INDEX `idx_username`(`username`) USING BTREE,
  INDEX `idx_email`(`email`) USING BTREE,
  INDEX `idx_phone`(`phone`) USING BTREE,
  INDEX `idx_is_active`(`is_active`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE,
  INDEX `idx_role`(`role`) USING BTREE,
  INDEX `idx_last_login_at`(`last_login_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '管理员用户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of zc_admin_users
-- ----------------------------
INSERT INTO `zc_admin_users` VALUES (1, 'admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '系统管理员', '<EMAIL>', NULL, NULL, 'super_admin', NULL, 1, NULL, NULL, '2025-08-13 16:40:54', '2025-08-13 16:40:54');

-- ----------------------------
-- Table structure for zc_app_categories
-- ----------------------------
DROP TABLE IF EXISTS `zc_app_categories`;
CREATE TABLE `zc_app_categories`  (
  `category_id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `category_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类名称',
  `category_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类代码',
  `parent_id` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '父分类ID',
  `category_icon` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '分类图标URL',
  `sort_order` int(11) NULL DEFAULT 0 COMMENT '排序顺序',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用',
  `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `updated_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`category_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'APP分类表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of zc_app_categories
-- ----------------------------
INSERT INTO `zc_app_categories` VALUES (1, '合成游戏', 'social', NULL, NULL, 1, 1, '2025-08-13 16:41:22', '2025-08-14 11:26:31');
INSERT INTO `zc_app_categories` VALUES (2, '短剧', 'finance', NULL, NULL, 2, 1, '2025-08-13 16:41:22', '2025-08-14 11:26:36');
INSERT INTO `zc_app_categories` VALUES (3, '阅读', 'shopping', NULL, NULL, 3, 1, '2025-08-13 16:41:22', '2025-08-14 11:26:41');
INSERT INTO `zc_app_categories` VALUES (4, '走路', 'lifestyle', NULL, NULL, 4, 1, '2025-08-13 16:41:22', '2025-08-14 11:26:44');
INSERT INTO `zc_app_categories` VALUES (5, '答题', 'entertainment', NULL, NULL, 5, 1, '2025-08-13 16:41:22', '2025-08-14 11:26:48');
INSERT INTO `zc_app_categories` VALUES (6, '其它', 'education', NULL, NULL, 6, 1, '2025-08-13 16:41:22', '2025-08-14 11:26:53');

-- ----------------------------
-- Table structure for zc_apps
-- ----------------------------
DROP TABLE IF EXISTS `zc_apps`;
CREATE TABLE `zc_apps`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `category_id` int(11) UNSIGNED NOT NULL COMMENT '分类ID',
  `app_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '应用名称',
  `app_package` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '应用包名',
  `app_version` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '应用版本',
  `developer` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '开发商',
  `app_label` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '标签',
  `app_size` bigint(20) NULL DEFAULT NULL COMMENT '应用大小(字节)',
  `download_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '下载链接',
  `logo_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'Logo图片URL',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '应用描述',
  `features` json NULL COMMENT '应用特性(JSON格式)',
  `screenshots` json NULL COMMENT '应用截图(JSON格式)',
  `rating` decimal(3, 2) NULL DEFAULT 0.00 COMMENT '平均评分',
  `rating_count` int(11) NULL DEFAULT 0 COMMENT '评分人数',
  `download_count` bigint(20) NULL DEFAULT 0 COMMENT '下载次数',
  `view_count` bigint(20) NULL DEFAULT 0 COMMENT '查看次数',
  `status` tinyint(3) UNSIGNED NULL DEFAULT 1 COMMENT '应用状态',
  `is_featured` tinyint(1) NULL DEFAULT 0 COMMENT '是否推荐',
  `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `updated_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_category_id`(`category_id`) USING BTREE,
  INDEX `idx_app_name`(`app_name`) USING BTREE,
  INDEX `idx_app_package`(`app_package`) USING BTREE,
  INDEX `idx_rating`(`rating`) USING BTREE,
  INDEX `idx_download_count`(`download_count`) USING BTREE,
  INDEX `idx_view_count`(`view_count`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_is_featured`(`is_featured`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE,
  INDEX `idx_category_status`(`category_id`, `status`) USING BTREE,
  INDEX `idx_status_featured`(`status`, `is_featured`) USING BTREE,
  INDEX `idx_status_rating`(`status`, `rating`) USING BTREE,
  INDEX `idx_status_downloads`(`status`, `download_count`) USING BTREE,
  INDEX `idx_featured_rating`(`is_featured`, `rating`) USING BTREE,
  FULLTEXT INDEX `ft_app_name_desc`(`app_name`, `description`)
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'APP信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of zc_apps
-- ----------------------------
INSERT INTO `zc_apps` VALUES (13, 2, '测试4', 'com.dd.cn', '1.2.3', NULL, '', NULL, 'https://www.ab.com/', 'http://citui.test.com/upload/image/20250818/17554855549877d4QV.png', NULL, NULL, NULL, 1.00, 0, 0, 0, 1, 0, '2025-08-19 17:30:28', '2025-08-19 17:30:28');
INSERT INTO `zc_apps` VALUES (14, 4, '2测试3', 'com.aaa.cc', '1.2.6', NULL, '热门,秒提现,免费试用', NULL, 'https://www.abn.com', 'http://citui.test.com/upload/image/20250819/175559209515042114.png', NULL, NULL, NULL, 3.00, 0, 220, 0, 1, 0, '2025-08-19 16:30:39', '2025-08-19 16:30:39');

-- ----------------------------
-- Table structure for zc_evaluation_reports
-- ----------------------------
DROP TABLE IF EXISTS `zc_evaluation_reports`;
CREATE TABLE `zc_evaluation_reports`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '报告ID',
  `app_id` bigint(20) UNSIGNED NOT NULL COMMENT '应用ID',
  `user_id` bigint(20) UNSIGNED NOT NULL COMMENT '用户ID',
  `report_title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '报告标题',
  `report_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '报告内容',
  `download_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '下载地址',
  `pingfen` smallint(3) UNSIGNED NULL DEFAULT 1 COMMENT '评分(1-5)',
  `yunxingmoshi` tinyint(3) UNSIGNED NULL DEFAULT 2 COMMENT '运行模式 1自动  2手动',
  `xinrenfuli` decimal(10, 2) UNSIGNED NULL DEFAULT NULL COMMENT '新人福利',
  `tixianmenkan` decimal(10, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '提现门槛',
  `dingbaojine` decimal(10, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '顶包金额',
  `ceshitiaoshu` smallint(5) UNSIGNED NULL DEFAULT 1 COMMENT '测试条数',
  `ceshishouyi` decimal(10, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '测试总收益',
  `ceshishichang` decimal(10, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '测试时长',
  `ceshishebei` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '测试设备',
  `cepingren` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '0' COMMENT '测评人',
  `status` tinyint(3) UNSIGNED NULL DEFAULT 1 COMMENT '状态',
  `view_count` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '查看次数',
  `like_count` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '点赞次数',
  `is_featured` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '是否推荐',
  `cepingriqi` date NULL DEFAULT NULL COMMENT '测试日期',
  `shouyi_1` decimal(10, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '第一条价格',
  `shouyi_2` decimal(10, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '第二条价格',
  `shouyi_3` decimal(10, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '第三条价格',
  `shouyi_4` decimal(10, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '第四条价格',
  `shouyi_5` decimal(10, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '第五条价格',
  `pic_main` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '主图',
  `pic_tixian` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '提现记录截图',
  `pic_daozhang` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '到账记录截图',
  `submitted_at` timestamp(0) NULL DEFAULT NULL COMMENT '提交时间',
  `approved_at` timestamp(0) NULL DEFAULT NULL COMMENT '审核通过时间',
  `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `updated_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '评测报告表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of zc_evaluation_reports
-- ----------------------------
INSERT INTO `zc_evaluation_reports` VALUES (2, 13, 3, '测试4 评测报告', '这是测评报告内容', 'https://www.ab.com/', 1, 1, 0.50, 1.00, 1.50, 2, 1.20, 10.00, '苹果16', '我自己', 1, 0, 0, 0, '2025-08-18', 0.10, 0.30, 0.05, 0.03, 0.02, 'upload/image/20250818/17554856315341lm7T.png', 'upload/image/20250818/17554856376126jJh8.png', 'upload/image/20250818/17554856419151oOLx.png', '2025-08-19 17:30:28', NULL, '2025-08-19 17:30:28', '2025-08-19 17:30:28');
INSERT INTO `zc_evaluation_reports` VALUES (3, 14, 3, '2测试3 评测报告', '2这是测评报告！！！！！！！！！！', 'https://www.abn.com', 3, 1, 1.00, 2.00, 3.00, 10, 12.00, 14.00, '华为', '555', 1, 0, 0, 0, '2025-08-18', 1.00, 2.00, 3.00, 4.00, 5.00, 'upload/image/20250818/17554869395510CWuD.png', 'upload/image/20250818/17554869466833bb7k.png', 'upload/image/20250818/17554869508404FS42.png', '2025-08-19 16:30:39', NULL, '2025-08-19 16:30:39', '2025-08-19 16:30:39');

-- ----------------------------
-- Table structure for zc_system_logs
-- ----------------------------
DROP TABLE IF EXISTS `zc_system_logs`;
CREATE TABLE `zc_system_logs`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `content` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `type` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '',
  `add_time` timestamp(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 98 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of zc_system_logs
-- ----------------------------
INSERT INTO `zc_system_logs` VALUES (1, 'Undefined array key \"data\"  /app/Helpers/Traits/ApiResponse.php  163', 'log', '2025-08-13 20:21:31');
INSERT INTO `zc_system_logs` VALUES (2, 'Undefined array key \"data\"  /app/Helpers/Traits/ApiResponse.php  163', 'log', '2025-08-13 20:22:45');
INSERT INTO `zc_system_logs` VALUES (3, 'syntax error, unexpected token \"=\"  /app/Services/Citui/AppService.php  42', 'log', '2025-08-13 20:41:42');
INSERT INTO `zc_system_logs` VALUES (4, 'Division by zero  /app/Services/Citui/AppService.php  43', 'log', '2025-08-13 20:42:16');
INSERT INTO `zc_system_logs` VALUES (5, 'Undefined array key \"data\"  /app/Helpers/Traits/ApiResponse.php  163', 'log', '2025-08-13 20:44:11');
INSERT INTO `zc_system_logs` VALUES (6, 'Undefined array key \"data\"  /app/Helpers/Traits/ApiResponse.php  163', 'log', '2025-08-13 20:48:05');
INSERT INTO `zc_system_logs` VALUES (7, 'Undefined array key \"data\"  /app/Helpers/Traits/ApiResponse.php  163', 'log', '2025-08-13 20:48:32');
INSERT INTO `zc_system_logs` VALUES (8, 'Undefined array key \"data\"  /app/Helpers/Traits/ApiResponse.php  163', 'log', '2025-08-13 20:49:05');
INSERT INTO `zc_system_logs` VALUES (9, '  /vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php  44', 'log', '2025-08-13 22:28:30');
INSERT INTO `zc_system_logs` VALUES (10, 'SQLSTATE[42S02]: Base table or view not found: 1146 Table \'citui.zc_user\' doesn\'t exist (SQL: select * from `zc_user` where `phone` = *********** limit 1)  /vendor/laravel/framework/src/Illuminate/Database/Connection.php  759', 'log', '2025-08-13 22:32:23');
INSERT INTO `zc_system_logs` VALUES (11, 'SQLSTATE[42S22]: Column not found: 1054 Unknown column \'invite_code\' in \'where clause\' (SQL: select exists(select * from `zc_user` where `invite_code` = 704472) as `exists`)  /vendor/laravel/framework/src/Illuminate/Database/Connection.php  759', 'log', '2025-08-13 22:34:25');
INSERT INTO `zc_system_logs` VALUES (12, 'SQLSTATE[42S22]: Column not found: 1054 Unknown column \'invite_code\' in \'where clause\' (SQL: select exists(select * from `zc_user` where `invite_code` = 261930) as `exists`)  /vendor/laravel/framework/src/Illuminate/Database/Connection.php  759', 'log', '2025-08-13 22:34:58');
INSERT INTO `zc_system_logs` VALUES (13, '注册失败  /app/Service/User/UserService.php  271', 'log', '2025-08-13 22:52:02');
INSERT INTO `zc_system_logs` VALUES (14, '注册失败  /app/Service/User/UserService.php  271', 'log', '2025-08-13 22:53:15');
INSERT INTO `zc_system_logs` VALUES (15, '注册失败  /app/Service/User/UserService.php  178', 'log', '2025-08-13 22:56:04');
INSERT INTO `zc_system_logs` VALUES (16, '注册失败  /app/Service/User/UserService.php  178', 'log', '2025-08-13 23:02:37');
INSERT INTO `zc_system_logs` VALUES (17, '注册失败  /app/Service/User/UserService.php  179', 'log', '2025-08-13 23:03:47');
INSERT INTO `zc_system_logs` VALUES (18, 'Undefined array key \"pwd\"  /app/Service/User/UserService.php  184', 'log', '2025-08-13 23:04:14');
INSERT INTO `zc_system_logs` VALUES (19, '该手机号已注册  /app/Service/User/UserService.php  153', 'log', '2025-08-13 23:06:53');
INSERT INTO `zc_system_logs` VALUES (20, '该手机号已注册  /app/Service/User/UserService.php  153', 'log', '2025-08-13 23:07:55');
INSERT INTO `zc_system_logs` VALUES (21, '  /vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php  44', 'log', '2025-08-14 00:11:13');
INSERT INTO `zc_system_logs` VALUES (22, '  /vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php  44', 'log', '2025-08-14 00:12:36');
INSERT INTO `zc_system_logs` VALUES (23, '  /vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php  44', 'log', '2025-08-14 00:13:53');
INSERT INTO `zc_system_logs` VALUES (24, '该手机号已注册  /app/Service/User/UserService.php  153', 'log', '2025-08-14 00:14:43');
INSERT INTO `zc_system_logs` VALUES (25, '  /vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php  44', 'log', '2025-08-14 00:18:10');
INSERT INTO `zc_system_logs` VALUES (26, '  /vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php  44', 'log', '2025-08-14 00:18:57');
INSERT INTO `zc_system_logs` VALUES (27, '  /vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php  44', 'log', '2025-08-14 00:19:40');
INSERT INTO `zc_system_logs` VALUES (28, '  /vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php  44', 'log', '2025-08-14 00:21:42');
INSERT INTO `zc_system_logs` VALUES (29, '该手机号已注册  /app/Service/User/UserService.php  153', 'log', '2025-08-14 00:22:37');
INSERT INTO `zc_system_logs` VALUES (30, 'require(D:\\mywork\\cituiproject\\cituilaravel\\routes/citui.php): Failed to open stream: No such file or directory  /vendor/laravel/framework/src/Illuminate/Routing/RouteFileRegistrar.php  35', 'log', '2025-08-14 09:59:43');
INSERT INTO `zc_system_logs` VALUES (31, 'require(D:\\mywork\\cituiproject\\cituilaravel\\routes/citui.php): Failed to open stream: No such file or directory  /vendor/laravel/framework/src/Illuminate/Routing/RouteFileRegistrar.php  35', 'log', '2025-08-14 10:02:30');
INSERT INTO `zc_system_logs` VALUES (32, 'require(D:\\mywork\\cituiproject\\cituilaravel\\routes/citui.php): Failed to open stream: No such file or directory  /vendor/laravel/framework/src/Illuminate/Routing/RouteFileRegistrar.php  35', 'log', '2025-08-14 10:08:08');
INSERT INTO `zc_system_logs` VALUES (33, 'require(D:\\mywork\\cituiproject\\cituilaravel\\routes/citui.php): Failed to open stream: No such file or directory  /vendor/laravel/framework/src/Illuminate/Routing/RouteFileRegistrar.php  35', 'log', '2025-08-14 10:08:38');
INSERT INTO `zc_system_logs` VALUES (34, '您的账号密码错误  /app/Service/User/UserService.php  31', 'log', '2025-08-14 10:09:16');
INSERT INTO `zc_system_logs` VALUES (35, '提交失败，请重试  /app/Service/Report/EvaluationReportService.php  63', 'log', '2025-08-18 10:11:48');
INSERT INTO `zc_system_logs` VALUES (36, '提交失败，请重试  /app/Service/Report/EvaluationReportService.php  63', 'log', '2025-08-18 10:13:46');
INSERT INTO `zc_system_logs` VALUES (37, 'date(): Argument #2 ($timestamp) must be of type ?int, float given  /app/Service/Report/EvaluationReportService.php  164', 'log', '2025-08-18 10:14:57');
INSERT INTO `zc_system_logs` VALUES (38, '测评报告内容不能为空  /app/Service/Report/EvaluationReportService.php  85', 'log', '2025-08-18 10:54:05');
INSERT INTO `zc_system_logs` VALUES (39, '  /vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php  44', 'log', '2025-08-18 14:58:18');
INSERT INTO `zc_system_logs` VALUES (40, '  /vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php  44', 'log', '2025-08-18 14:58:18');
INSERT INTO `zc_system_logs` VALUES (41, '  /vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php  44', 'log', '2025-08-18 14:58:18');
INSERT INTO `zc_system_logs` VALUES (42, '  /vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php  44', 'log', '2025-08-18 14:58:18');
INSERT INTO `zc_system_logs` VALUES (43, '  /vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php  44', 'log', '2025-08-18 14:58:18');
INSERT INTO `zc_system_logs` VALUES (44, '  /vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php  44', 'log', '2025-08-18 14:58:32');
INSERT INTO `zc_system_logs` VALUES (45, '  /vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php  44', 'log', '2025-08-18 14:58:32');
INSERT INTO `zc_system_logs` VALUES (46, '  /vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php  44', 'log', '2025-08-18 14:58:32');
INSERT INTO `zc_system_logs` VALUES (47, '  /vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php  44', 'log', '2025-08-18 14:58:33');
INSERT INTO `zc_system_logs` VALUES (48, '  /vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php  44', 'log', '2025-08-18 14:58:33');
INSERT INTO `zc_system_logs` VALUES (49, '  /vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php  44', 'log', '2025-08-18 14:59:17');
INSERT INTO `zc_system_logs` VALUES (50, '  /vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php  44', 'log', '2025-08-18 14:59:17');
INSERT INTO `zc_system_logs` VALUES (51, '  /vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php  44', 'log', '2025-08-18 14:59:17');
INSERT INTO `zc_system_logs` VALUES (52, '  /vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php  44', 'log', '2025-08-18 14:59:17');
INSERT INTO `zc_system_logs` VALUES (53, '  /vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php  44', 'log', '2025-08-18 14:59:17');
INSERT INTO `zc_system_logs` VALUES (54, '  /vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php  44', 'log', '2025-08-18 14:59:35');
INSERT INTO `zc_system_logs` VALUES (55, '  /vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php  44', 'log', '2025-08-18 14:59:35');
INSERT INTO `zc_system_logs` VALUES (56, '  /vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php  44', 'log', '2025-08-18 14:59:35');
INSERT INTO `zc_system_logs` VALUES (57, '  /vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php  44', 'log', '2025-08-18 14:59:35');
INSERT INTO `zc_system_logs` VALUES (58, '  /vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php  44', 'log', '2025-08-18 14:59:35');
INSERT INTO `zc_system_logs` VALUES (59, '  /vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php  44', 'log', '2025-08-18 14:59:52');
INSERT INTO `zc_system_logs` VALUES (60, '  /vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php  44', 'log', '2025-08-18 14:59:52');
INSERT INTO `zc_system_logs` VALUES (61, '  /vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php  44', 'log', '2025-08-18 14:59:52');
INSERT INTO `zc_system_logs` VALUES (62, '  /vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php  44', 'log', '2025-08-18 14:59:52');
INSERT INTO `zc_system_logs` VALUES (63, '  /vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php  44', 'log', '2025-08-18 14:59:52');
INSERT INTO `zc_system_logs` VALUES (64, '请先登录  /app/Service/User/Auth/LoginService.php  31', 'log', '2025-08-18 16:44:16');
INSERT INTO `zc_system_logs` VALUES (65, 'SQLSTATE[42S02]: Base table or view not found: 1146 Table \'citui.zc_zc_apps\' doesn\'t exist (SQL: select count(*) as aggregate from `zc_zc_apps` where `id` = 14)  /vendor/laravel/framework/src/Illuminate/Database/Connection.php  759', 'log', '2025-08-19 09:49:07');
INSERT INTO `zc_system_logs` VALUES (66, '请先登录  /app/Service/User/Auth/LoginService.php  31', 'log', '2025-08-19 10:11:27');
INSERT INTO `zc_system_logs` VALUES (67, '请先登录  /app/Service/User/Auth/LoginService.php  31', 'log', '2025-08-19 10:11:44');
INSERT INTO `zc_system_logs` VALUES (68, '请先登录  /app/Service/User/Auth/LoginService.php  31', 'log', '2025-08-19 10:12:45');
INSERT INTO `zc_system_logs` VALUES (69, '请先登录  /app/Service/User/Auth/LoginService.php  31', 'log', '2025-08-19 10:19:40');
INSERT INTO `zc_system_logs` VALUES (70, '请先登录  /app/Service/User/Auth/LoginService.php  31', 'log', '2025-08-19 10:19:51');
INSERT INTO `zc_system_logs` VALUES (71, '请先登录  /app/Service/User/Auth/LoginService.php  31', 'log', '2025-08-19 10:20:00');
INSERT INTO `zc_system_logs` VALUES (72, 'APP不存在或已下架  /app/Service/App/AppService.php  221', 'log', '2025-08-19 11:55:20');
INSERT INTO `zc_system_logs` VALUES (73, 'APP不存在  /app/Service/Clue/ClueService.php  306', 'log', '2025-08-19 11:55:20');
INSERT INTO `zc_system_logs` VALUES (74, 'APP不存在  /app/Service/Clue/ClueService.php  306', 'log', '2025-08-19 11:55:35');
INSERT INTO `zc_system_logs` VALUES (75, 'APP不存在或已下架  /app/Service/App/AppService.php  221', 'log', '2025-08-19 11:55:35');
INSERT INTO `zc_system_logs` VALUES (76, 'APP不存在或已下架  /app/Service/App/AppService.php  221', 'log', '2025-08-19 11:55:55');
INSERT INTO `zc_system_logs` VALUES (77, 'APP不存在  /app/Service/Clue/ClueService.php  306', 'log', '2025-08-19 11:55:55');
INSERT INTO `zc_system_logs` VALUES (78, '请先登录  /app/Service/User/Auth/LoginService.php  31', 'log', '2025-08-19 14:09:35');
INSERT INTO `zc_system_logs` VALUES (79, '请先登录  /app/Service/User/Auth/LoginService.php  31', 'log', '2025-08-19 14:10:16');
INSERT INTO `zc_system_logs` VALUES (80, '请先登录  /app/Service/User/Auth/LoginService.php  31', 'log', '2025-08-19 14:11:54');
INSERT INTO `zc_system_logs` VALUES (81, '请先登录  /app/Service/User/Auth/LoginService.php  31', 'log', '2025-08-19 14:11:56');
INSERT INTO `zc_system_logs` VALUES (82, '请先登录  /app/Service/User/Auth/LoginService.php  31', 'log', '2025-08-19 14:12:02');
INSERT INTO `zc_system_logs` VALUES (83, '请先登录  /app/Service/User/Auth/LoginService.php  31', 'log', '2025-08-19 14:12:45');
INSERT INTO `zc_system_logs` VALUES (84, '请先登录  /app/Service/User/Auth/LoginService.php  31', 'log', '2025-08-19 14:12:54');
INSERT INTO `zc_system_logs` VALUES (85, '请先登录  /app/Service/User/Auth/LoginService.php  31', 'log', '2025-08-19 14:13:31');
INSERT INTO `zc_system_logs` VALUES (86, '请先登录  /app/Service/User/Auth/LoginService.php  31', 'log', '2025-08-19 14:15:17');
INSERT INTO `zc_system_logs` VALUES (87, '请先登录  /app/Service/User/Auth/LoginService.php  31', 'log', '2025-08-19 14:18:10');
INSERT INTO `zc_system_logs` VALUES (88, '请先登录  /app/Service/User/Auth/LoginService.php  31', 'log', '2025-08-19 14:19:34');
INSERT INTO `zc_system_logs` VALUES (89, '请先登录  /app/Service/User/Auth/LoginService.php  31', 'log', '2025-08-19 14:22:13');
INSERT INTO `zc_system_logs` VALUES (90, '请先登录  /app/Service/Report/EvaluationReportService.php  242', 'log', '2025-08-19 14:38:53');
INSERT INTO `zc_system_logs` VALUES (91, '请先登录  /app/Service/Report/EvaluationReportService.php  242', 'log', '2025-08-19 14:39:08');
INSERT INTO `zc_system_logs` VALUES (92, '请先登录  /app/Service/Report/EvaluationReportService.php  242', 'log', '2025-08-19 14:39:55');
INSERT INTO `zc_system_logs` VALUES (93, '请先登录  /app/Service/Report/EvaluationReportService.php  242', 'log', '2025-08-19 14:43:57');
INSERT INTO `zc_system_logs` VALUES (94, '请先登录  /app/Service/Report/EvaluationReportService.php  242', 'log', '2025-08-19 14:44:04');
INSERT INTO `zc_system_logs` VALUES (95, 'APP不存在或已下架  /app/Service/App/AppService.php  221', 'log', '2025-08-19 14:59:58');
INSERT INTO `zc_system_logs` VALUES (96, 'APP不存在  /app/Service/Clue/ClueService.php  335', 'log', '2025-08-19 14:59:58');
INSERT INTO `zc_system_logs` VALUES (97, '请先登录  /app/Service/User/Auth/LoginService.php  31', 'log', '2025-08-19 16:40:17');

-- ----------------------------
-- Table structure for zc_user
-- ----------------------------
DROP TABLE IF EXISTS `zc_user`;
CREATE TABLE `zc_user`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `pid` bigint(20) UNSIGNED NOT NULL DEFAULT 0,
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '手机号码',
  `pwd` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '密码',
  `nick_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '用户昵称',
  `real_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '真实姓名',
  `avatar_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '头像URL',
  `gender` tinyint(4) NULL DEFAULT 0 COMMENT '性别',
  `birthday` date NULL DEFAULT NULL COMMENT '生日',
  `province` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '省份',
  `city` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '城市',
  `total_points` int(11) NULL DEFAULT 0 COMMENT '总积分',
  `available_points` int(11) NULL DEFAULT 0 COMMENT '可用积分',
  `level` int(11) NULL DEFAULT 1 COMMENT '用户等级',
  `invite_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '推广码',
  `is_admin` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '是否是管理员 只有管理员才能添加测评和放水线索',
  `status` tinyint(3) UNSIGNED NULL DEFAULT 1 COMMENT '用户状态',
  `last_login_at` timestamp(0) NULL DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '最后登录IP',
  `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `updated_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_phone`(`phone`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of zc_user
-- ----------------------------
INSERT INTO `zc_user` VALUES (3, 0, '***********', '96e79218965eb72c92a549dd5a330112', '', '', '', 0, NULL, '', '', 0, 0, 1, '825522', 1, 1, '2025-08-19 16:42:26', '127.0.0.1', '2025-08-13 23:08:11', '2025-08-19 16:42:26');
INSERT INTO `zc_user` VALUES (4, 0, '13310111011', '96e79218965eb72c92a549dd5a330112', '', '', '', 0, NULL, '', '', 0, 0, 1, '867725', 1, 1, '2025-08-14 00:22:50', '127.0.0.1', '2025-08-14 00:22:50', '2025-08-19 11:01:15');

-- ----------------------------
-- Table structure for zc_user_logins
-- ----------------------------
DROP TABLE IF EXISTS `zc_user_logins`;
CREATE TABLE `zc_user_logins`  (
  `login_id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '登录记录ID',
  `user_id` bigint(20) UNSIGNED NOT NULL COMMENT '用户ID',
  `login_type` enum('password','sms','wechat','qq') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'password' COMMENT '登录方式',
  `login_ip` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '登录IP地址',
  `login_device` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '登录设备信息',
  `user_agent` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '用户代理',
  `login_status` enum('success','failed') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'success' COMMENT '登录状态',
  `failure_reason` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '失败原因',
  `session_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '会话ID',
  `login_duration` int(11) NULL DEFAULT NULL COMMENT '登录时长(秒)',
  `logout_at` timestamp(0) NULL DEFAULT NULL COMMENT '登出时间',
  `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '登录时间',
  PRIMARY KEY (`login_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_login_ip`(`login_ip`) USING BTREE,
  INDEX `idx_login_status`(`login_status`) USING BTREE,
  INDEX `idx_login_type`(`login_type`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE,
  INDEX `idx_session_id`(`session_id`) USING BTREE,
  INDEX `idx_user_status`(`user_id`, `login_status`) USING BTREE,
  INDEX `idx_user_created`(`user_id`, `created_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户登录记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for zc_water_clues
-- ----------------------------
DROP TABLE IF EXISTS `zc_water_clues`;
CREATE TABLE `zc_water_clues`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '线索ID',
  `app_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '应用ID',
  `user_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户ID',
  `nick_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '发布人名称-用户自定义昵称',
  `clue_title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '线索标题-后端接口拼接 前端不需要传递',
  `clue_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '线索描述内容',
  `clue_time` datetime(0) NULL DEFAULT NULL COMMENT '放水时间 年月日 时分秒',
  `clue_money` decimal(10, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '放水金额',
  `package_money` tinyint(3) UNSIGNED NULL DEFAULT 1 COMMENT '单包大小 默认1 表示0.1-0.29  2表示 0.3-0.49  3表示 0.5-0.99  4表示1以上  ',
  `device_model` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '设备型号',
  `pic_tixian` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT 'APP提现记录截图',
  `pic_daozhang` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '微信到账记录截图',
  `status` tinyint(3) UNSIGNED NULL DEFAULT 1 COMMENT '状态',
  `expires_at` timestamp(0) NULL DEFAULT NULL COMMENT '过期时间',
  `submitted_at` timestamp(0) NULL DEFAULT NULL COMMENT '提交时间',
  `approved_at` timestamp(0) NULL DEFAULT NULL COMMENT '审核通过时间',
  `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `updated_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '放水线索表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of zc_water_clues
-- ----------------------------
INSERT INTO `zc_water_clues` VALUES (3, 14, 3, '测试人', '2测试3 - 放水线索 - 2025-08-19 17:05', '手动阀手动阀富士达阿达是放大发撒', '2025-08-19 17:04:59', 2.10, 2, '阿斯顿发生', 'http://citui.test.com/upload/image/20250819/175559432759786dfJ.png', 'http://citui.test.com/upload/image/20250819/17555943335619qoZV.png', 1, NULL, '2025-08-19 17:05:34', NULL, '2025-08-19 17:05:34', '2025-08-19 17:08:45');

SET FOREIGN_KEY_CHECKS = 1;
