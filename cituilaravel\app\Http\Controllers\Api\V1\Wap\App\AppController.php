<?php
declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Wap\App;

use App\Http\Controllers\Api\Controller;
use App\Service\App\AppService;

class AppController extends Controller
{
    protected AppService $appService;
    
    public function __construct(AppService $appService)
    {
        $this->appService = $appService;
        parent::__construct();
    }

    /**
     * 获取首页数据
     * @return \Illuminate\Http\JsonResponse
     */
    public function getHomeData()
    {
        return $this->apiSuccess($this->appService->getHomeData());
    }

    /**
     * 获取APP详情信息
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAppDetail()
    {
        return $this->apiSuccess($this->appService->getAppDetail());
    }
}
