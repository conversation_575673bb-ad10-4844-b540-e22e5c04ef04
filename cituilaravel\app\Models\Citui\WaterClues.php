<?php
declare(strict_types=1);

namespace App\Models\Citui;

use App\Models\BaseModel;

class WaterClues extends BaseModel
{
    protected $table = 'water_clues';

    protected $fillable = [
        'app_id',
        'user_id',
        'clue_title',
        'clue_content',
        'clue_time',
        'clue_money',
        'package_money',
        'device_model',
        'nick_name',
        'pic_tixian',
        'pic_daozhang',
        'status',
        'expires_at',
        'submitted_at',
        'approved_at'
    ];

    protected $casts = [
        'clue_time' => 'datetime',
        'clue_money' => 'decimal:2',
        'package_money' => 'integer',
        'status' => 'integer',
        'expires_at' => 'datetime',
        'submitted_at' => 'datetime',
        'approved_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 关联APP
     */
    public function app()
    {
        return $this->belongsTo(App::class, 'app_id', 'id');
    }

    /**
     * 单包大小映射
     */
    public static function getPackageMoneyOptions(): array
    {
        return [
            1 => '0.1-0.29',
            2 => '0.3-0.49',
            3 => '0.5-0.99',
            4 => '1以上'
        ];
    }

    /**
     * 获取单包大小文本
     */
    public function getPackageMoneyTextAttribute(): string
    {
        $options = self::getPackageMoneyOptions();
        return $options[$this->package_money] ?? '';
    }
}