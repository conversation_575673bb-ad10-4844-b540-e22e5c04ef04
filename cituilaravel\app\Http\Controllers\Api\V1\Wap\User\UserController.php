<?php
declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Wap\User;

use App\Http\Controllers\Api\Controller;
use App\Http\Requests\Front\UserRegRequest;
use App\Service\Order\OrderService;
use App\Service\User\UserPriceRecordService;
use App\Service\User\UserService;
use App\Service\User\UserStatsService;
use App\Utils\Tools;
use Illuminate\Support\Str;

class UserController extends Controller
{
    protected UserService $userService;
    protected UserStatsService $userStatsService;

    public function __construct(UserService $userService, UserStatsService $userStatsService)
    {
        $this->userService = $userService;
        $this->userStatsService = $userStatsService;
        parent::__construct();
    }

    public function loginSms(){
        return $this->apiSuccess($this->userService->loginSms());
    }

    public function register(){
        return $this->apiSuccess($this->userService->register());
    }

    public function regH5(){
        return $this->apiSuccess($this->userService->regH5());
    }

    public function getUserInfo(){
        $user = request()->attributes->get('user');
        unset($user['pwd']);
        $copy_desc = '';
        $download_url = Tools::getFixedDownload();
        $download_url_tap = Tools::getTapDownload();
        if(request()->get('copy') == 1){
            $copy_desc = Tools::getRandomCopyDesc();
            $copy_desc = str_replace('******', $user['invite_code'], $copy_desc);
            $copy_desc = str_replace('{reg_code}', $user['invite_code'], $copy_desc);
            if(Str::contains($copy_desc,'{download}')){
                if(!empty($download_url) || !empty($download_url_tap)){
                    if($download_url && $download_url_tap){
                        $download_desc = "\r\n下载地址1：".$download_url_tap."\r\n下载地址2：".$download_url;
                        $copy_desc = str_replace('{download}', $download_desc, $copy_desc);
                    }else{
                        $download_desc = "\r\n下载地址：".$download_url?:$download_url_tap;
                        $copy_desc = str_replace('{download}', $download_desc, $copy_desc);
                    }
                }
            }

        }
        return $this->apiSuccess([
                                    'user'         => $user,
                                    'copy_desc'    => $copy_desc,
                                    'download_url' => $download_url,
                                    'download_url_tap' => $download_url_tap
                                ]);
    }


    public function getUser(){
        $user = request()->attributes->get('user');
        if(is_array($user)){
            if(isset($user['money'])){
                $user['money'] = format_money($user['money'], 2);
            }
        }

        $user['site_name'] = config('ad.system_name');

        return $this->apiSuccess($user);
    }

    /**
     * 获取用户统计数据
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUserStats()
    {
        $user = request()->attributes->get('user');
        $userId = $user['id'];

        $stats = $this->userStatsService->getUserStats($userId);

        return $this->apiSuccess($stats);
    }

}

