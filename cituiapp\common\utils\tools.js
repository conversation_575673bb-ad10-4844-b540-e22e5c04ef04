/**
 * 安全区域工具类
 * 用于处理自定义导航栏页面的顶部安全距离适配
 */

/**
 * 获取系统信息和安全区域信息
 * @returns {Object} 包含状态栏高度和安全区域信息的对象
 */
export const getSystemSafeArea = () => {
  try {
    const systemInfo = uni.getSystemInfoSync()

    // 获取状态栏高度
    const statusBarHeight = systemInfo.statusBarHeight || 0

    // 获取安全区域信息
    const safeAreaInsets = systemInfo.safeAreaInsets || {
      top: statusBarHeight,
      bottom: 0,
      left: 0,
      right: 0
    }

    // 计算顶部安全距离（状态栏高度 + 导航栏预留空间）
    // 在不同平台下可能需要不同的处理
    let topSafeDistance = statusBarHeight

    // #ifdef APP-PLUS
    // APP环境下，自定义导航栏需要额外的空间
    topSafeDistance = statusBarHeight
    // #endif

    // #ifdef MP-WEIXIN
    // 微信小程序环境
    const menuButtonInfo = uni.getMenuButtonBoundingClientRect()
    if (menuButtonInfo) {
      topSafeDistance = menuButtonInfo.bottom + 8 // 胶囊按钮底部 + 8px间距
    } else {
      topSafeDistance = statusBarHeight
    }
    // #endif

    // #ifdef H5
    // H5环境下通常不需要额外处理，但保留状态栏高度
    topSafeDistance = statusBarHeight
    // #endif

    return {
      statusBarHeight,
      safeAreaInsets,
      topSafeDistance,
      platform: systemInfo.platform,
      systemInfo
    }
  } catch (error) {
    console.error('获取系统安全区域信息失败:', error)
    // 返回默认值
    return {
      statusBarHeight: 20,
      safeAreaInsets: { top: 20, bottom: 0, left: 0, right: 0 },
      topSafeDistance: 64,
      platform: 'unknown',
      systemInfo: {}
    }
  }
}

/**
 * 将px转换为rpx
 * @param {number} px - 像素值
 * @returns {number} rpx值
 */
export const pxToRpx = (px) => {
  try {
    const systemInfo = uni.getSystemInfoSync()
    const screenWidth = systemInfo.screenWidth || 375
    return (px * 750) / screenWidth
  } catch (error) {
    // 默认按iPhone6的屏幕宽度计算
    return (px * 750) / 375
  }
}

/**
 * 获取顶部安全距离的rpx值
 * @returns {number} 顶部安全距离的rpx值
 */
export const getTopSafeDistanceRpx = () => {
  const { topSafeDistance } = getSystemSafeArea()
  return pxToRpx(topSafeDistance)
}