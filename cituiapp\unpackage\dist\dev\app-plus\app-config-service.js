
var isReady=false;var onReadyCallbacks=[];
var isServiceReady=false;var onServiceReadyCallbacks=[];
var __uniConfig = {"pages":["pages/index/index","pages/evaluation/evaluation","pages/clue/clue","pages/invite/invite","pages/profile/profile","pages/login/login","pages/detail/detail","pages/submit-report/submit-report","pages/submit-clue/submit-clue"],"window":{"navigationBarTextStyle":"black","navigationBarTitleText":"欢迎使用","navigationBarBackgroundColor":"#F8F8F8","backgroundColor":"#F8F8F8","bounce":"none"},"tabBar":{"color":"#bfbfbf","selectedColor":"#3662e3","backgroundColor":"#FFFFFF","list":[{"pagePath":"pages/index/index","text":"首页","iconPath":"static/tabbar/home_2.png","selectedIconPath":"static/tabbar/home_1.png"},{"pagePath":"pages/evaluation/evaluation","text":"评测","iconPath":"static/tabbar/pingce_2.png","selectedIconPath":"static/tabbar/pingce_1.png"},{"pagePath":"pages/clue/clue","text":"线索","iconPath":"static/tabbar/clue_2.png","selectedIconPath":"static/tabbar/clue_1.png"},{"pagePath":"pages/invite/invite","text":"邀请","iconPath":"static/tabbar/yaoqing_2.png","selectedIconPath":"static/tabbar/yaoqing_1.png"},{"pagePath":"pages/profile/profile","text":"我的","iconPath":"static/tabbar/my_2.png","selectedIconPath":"static/tabbar/my_1.png"}]},"darkmode":false,"nvueCompiler":"uni-app","nvueStyleCompiler":"uni-app","renderer":"auto","splashscreen":{"alwaysShowBeforeRender":true,"autoclose":false},"appname":"次推","compilerVersion":"4.75","entryPagePath":"pages/index/index","networkTimeout":{"request":60000,"connectSocket":60000,"uploadFile":60000,"downloadFile":60000}};
var __uniRoutes = [{"path":"/pages/index/index","meta":{"isQuit":true,"isTabBar":true},"window":{"navigationBarTitleText":"首页","navigationStyle":"custom"}},{"path":"/pages/evaluation/evaluation","meta":{"isQuit":true,"isTabBar":true},"window":{"navigationBarTitleText":"评测","navigationStyle":"custom"}},{"path":"/pages/clue/clue","meta":{"isQuit":true,"isTabBar":true},"window":{"navigationBarTitleText":"线索","navigationStyle":"custom"}},{"path":"/pages/invite/invite","meta":{"isQuit":true,"isTabBar":true},"window":{"navigationBarTitleText":"邀请","navigationStyle":"custom"}},{"path":"/pages/profile/profile","meta":{"isQuit":true,"isTabBar":true},"window":{"navigationBarTitleText":"我的","navigationStyle":"custom"}},{"path":"/pages/login/login","meta":{},"window":{"navigationBarTitleText":"登录","navigationStyle":"custom"}},{"path":"/pages/detail/detail","meta":{},"window":{"navigationBarTitleText":"详情","navigationStyle":"custom"}},{"path":"/pages/submit-report/submit-report","meta":{},"window":{"navigationBarTitleText":"提交评测报告","navigationStyle":"custom"}},{"path":"/pages/submit-clue/submit-clue","meta":{},"window":{"navigationBarTitleText":"提交放水线索","navigationStyle":"custom"}}];
__uniConfig.onReady=function(callback){if(__uniConfig.ready){callback()}else{onReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"ready",{get:function(){return isReady},set:function(val){isReady=val;if(!isReady){return}const callbacks=onReadyCallbacks.slice(0);onReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
__uniConfig.onServiceReady=function(callback){if(__uniConfig.serviceReady){callback()}else{onServiceReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"serviceReady",{get:function(){return isServiceReady},set:function(val){isServiceReady=val;if(!isServiceReady){return}const callbacks=onServiceReadyCallbacks.slice(0);onServiceReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
service.register("uni-app-config",{create(a,b,c){if(!__uniConfig.viewport){var d=b.weex.config.env.scale,e=b.weex.config.env.deviceWidth,f=Math.ceil(e/d);Object.assign(__uniConfig,{viewport:f,defaultFontSize:Math.round(f/20)})}return{instance:{__uniConfig:__uniConfig,__uniRoutes:__uniRoutes,global:void 0,window:void 0,document:void 0,frames:void 0,self:void 0,location:void 0,navigator:void 0,localStorage:void 0,history:void 0,Caches:void 0,screen:void 0,alert:void 0,confirm:void 0,prompt:void 0,fetch:void 0,XMLHttpRequest:void 0,WebSocket:void 0,webkit:void 0,print:void 0}}}});
