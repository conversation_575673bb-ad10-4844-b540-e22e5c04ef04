<!-- 
  安全区域适配示例页面
  展示如何在新页面中使用安全区域适配方案
-->
<template>
  <view class="page-container">
    <!-- 固定顶部导航 - 使用安全区域样式 -->
    <view class="fixed-header" :style="topSafeAreaStyle">
      <view class="header-content">
        <view class="back-btn" @click="goBack">
          <text class="back-icon">‹</text>
        </view>
        <view class="header-title">示例页面</view>
        <view class="header-placeholder"></view>
      </view>
    </view>
    
    <!-- 页面内容区域 - 使用安全区域样式 -->
    <view class="content-container" :style="contentTopMarginStyle">
      <scroll-view class="scroll-container" scroll-y>
        <view class="content-inner">
          <view class="info-card">
            <text class="info-title">安全区域信息</text>
            <view class="info-item">
              <text class="info-label">状态栏高度:</text>
              <text class="info-value">{{ safeAreaInfo.statusBarHeight }}px</text>
            </view>
            <view class="info-item">
              <text class="info-label">顶部安全距离:</text>
              <text class="info-value">{{ safeAreaInfo.topSafeDistance }}px</text>
            </view>
            <view class="info-item">
              <text class="info-label">顶部安全距离(rpx):</text>
              <text class="info-value">{{ safeAreaInfo.topSafeDistanceRpx }}rpx</text>
            </view>
            <view class="info-item">
              <text class="info-label">当前平台:</text>
              <text class="info-value">{{ safeAreaInfo.platform }}</text>
            </view>
          </view>
          
          <view class="demo-content">
            <text class="demo-text">这是一个演示内容区域</text>
            <text class="demo-text">内容会自动适配安全区域</text>
            <text class="demo-text">在不同设备上都能正确显示</text>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script>
// 1. 引入安全区域混入
import safeAreaMixin from '@/mixins/safeArea.js'

export default {
  // 2. 使用混入
  mixins: [safeAreaMixin],
  
  data() {
    return {
      // 页面数据
    }
  },
  
  methods: {
    goBack() {
      uni.navigateBack()
    }
  },
  
  onLoad() {
    console.log('页面加载，安全区域信息:', this.safeAreaInfo)
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  width: 100%;
  height: 100vh;
  background-color: #f5f5f5;
}

/* 固定顶部导航 */
.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  background-color: #ffffff;
  border-bottom: 2rpx solid #f0f0f0;
  
  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 32rpx;
    min-height: 88rpx;
    
    .back-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 48rpx;
      height: 48rpx;
      
      .back-icon {
        font-size: 48rpx;
        color: #333;
        font-weight: bold;
      }
    }
    
    .header-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }
    
    .header-placeholder {
      width: 48rpx;
      height: 48rpx;
    }
  }
}

/* 内容区域 */
.content-container {
  /* padding-top 通过动态样式设置，适配不同设备的安全区域 */
  height: 100vh;
  background-color: #f9fafb;
  
  .scroll-container {
    height: 100%;
    
    .content-inner {
      padding: 32rpx;
      
      .info-card {
        background: #ffffff;
        border-radius: 16rpx;
        padding: 32rpx;
        margin-bottom: 32rpx;
        box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.1);
        
        .info-title {
          font-size: 32rpx;
          font-weight: 600;
          color: #333;
          margin-bottom: 24rpx;
          display: block;
        }
        
        .info-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 16rpx 0;
          border-bottom: 1rpx solid #f0f0f0;
          
          &:last-child {
            border-bottom: none;
          }
          
          .info-label {
            font-size: 28rpx;
            color: #666;
          }
          
          .info-value {
            font-size: 28rpx;
            color: #333;
            font-weight: 500;
          }
        }
      }
      
      .demo-content {
        background: #ffffff;
        border-radius: 16rpx;
        padding: 32rpx;
        
        .demo-text {
          display: block;
          font-size: 28rpx;
          color: #666;
          line-height: 1.6;
          margin-bottom: 16rpx;
          
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }
}
</style>
