/**
 * 安全区域配置文件
 * 用于配置哪些页面需要顶部安全距离适配
 */

// 需要安全区域适配的页面路径列表
export const SAFE_AREA_PAGES = [
  'pages/index/index',
  'pages/clue/clue', 
  'pages/detail/detail',
  'pages/evaluation/evaluation',
  'pages/submit-clue/submit-clue',
  'pages/submit-report/submit-report'
]

/**
 * 检查当前页面是否需要安全区域适配
 * @param {string} pagePath - 页面路径
 * @returns {boolean}
 */
export const needSafeAreaAdapt = (pagePath) => {
  if (!pagePath) {
    return false
  }
  
  // 移除开头的斜杠
  const cleanPath = pagePath.startsWith('/') ? pagePath.substring(1) : pagePath
  
  return SAFE_AREA_PAGES.includes(cleanPath)
}

/**
 * 获取当前页面路径
 * @returns {string}
 */
export const getCurrentPagePath = () => {
  try {
    const pages = getCurrentPages()
    if (pages && pages.length > 0) {
      const currentPage = pages[pages.length - 1]
      return currentPage.route || ''
    }
  } catch (error) {
    console.error('获取当前页面路径失败:', error)
  }
  return ''
}

/**
 * 检查当前页面是否需要安全区域适配
 * @returns {boolean}
 */
export const currentPageNeedSafeArea = () => {
  const currentPath = getCurrentPagePath()
  return needSafeAreaAdapt(currentPath)
}
