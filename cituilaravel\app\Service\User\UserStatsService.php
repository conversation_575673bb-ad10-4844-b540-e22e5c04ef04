<?php
declare(strict_types=1);

namespace App\Service\User;

use App\Models\Citui\EvaluationReport;
use App\Models\Citui\WaterClues;
use App\Service\BaseService;

class UserStatsService extends BaseService
{
    /**
     * 获取用户统计数据
     *
     * @param int $userId 用户ID
     * @return array
     */
    public function getUserStats(int $userId): array
    {
        // 统计用户报告数量
        $reportCount = EvaluationReport::where('user_id', $userId)->count();
        
        // 统计用户线索数量
        $clueCount = WaterClues::where('user_id', $userId)->count();
        
        return [
            'report_count' => $reportCount,
            'clue_count' => $clueCount
        ];
    }
    
    /**
     * 获取用户报告统计数据
     *
     * @param int $userId 用户ID
     * @return array
     */
    public function getUserReportStats(int $userId): array
    {
        $query = EvaluationReport::where('user_id', $userId);
        
        return [
            'total_count' => $query->count(),
            'approved_count' => $query->whereNotNull('approved_at')->count(),
            'pending_count' => $query->whereNull('approved_at')->count()
        ];
    }
    
    /**
     * 获取用户线索统计数据
     *
     * @param int $userId 用户ID
     * @return array
     */
    public function getUserClueStats(int $userId): array
    {
        $query = WaterClues::where('user_id', $userId);
        
        return [
            'total_count' => $query->count(),
            'approved_count' => $query->whereNotNull('approved_at')->count(),
            'pending_count' => $query->whereNull('approved_at')->count()
        ];
    }
}
